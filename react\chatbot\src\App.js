import './App.css';
import { useEffect, useState } from 'react';
import { chatSnapShot } from './firebase';
import DisplayArea from './Components/DisplayArea/DisplayArea';
import TypeArea from './Components/TypeArea';

function App() {
  const [chatData,setChatData] = useState([]);
  const [mainCollection, setMainCollection] = useState();
  const [isWaiting,setIsWaiting] = useState(false);
  const [isConnecting,setIsConnecting] = useState(false);
  
  const [chatS, setChatS] = useState();
  useEffect(()=>{chatSnapShot(setChatData,setMainCollection,setIsWaiting)},[])
  return (
    <>
    <div className='container-fluid pe-0'>
      {/* <div className= "d-flex justify-content-center m-0" >
        <div className='welcome justify-content-center mb-0 mb-2'>
          Welcome to {window.pn} AI assistant
        </div>
      </div> */}
      <DisplayArea chatData={chatData} isWaiting={isWaiting} isConnecting={isConnecting} />
    </div>
    <div className='ftr'>
      <TypeArea mainCollection={mainCollection} isWaiting = {isWaiting} setIsWaiting={setIsWaiting} setChatData={setChatData} setIsConnecting= {setIsConnecting} setMainCollection={setMainCollection} setChatS={setChatS} chatS={chatS} chatData={chatData} />
    </div>
    </>
  );
}

export default App;
