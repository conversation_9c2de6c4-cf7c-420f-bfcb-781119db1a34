import { useEffect, useRef } from "react"
import { addQuestion, model } from "../firebase";


  
export default function TypeArea({
    mainCollection,
    isWaiting,
    setIsWaiting,
    setChatData,
    setIsConnecting, 
    setMainCollection,
    setChatS,
    chatS,
    chatData
}){
    const queryParams = new URLSearchParams(window.location.search);
    const is_app = queryParams.get("app")
    const tb = useRef();
    const btn = useRef();
    const handleKeyPress = (e)=>{
        if(e.key === "Enter"){
            handleClick();
        }
    }
    useEffect(()=>{
        tb.current.focus();
    },[])
    useEffect(()=>{
        if(!isWaiting){
            if(tb.current){
                tb.current.focus();
            }
        }
    },[isWaiting,setIsConnecting])
    const handleClick = ()=>{
        if(tb.current.value!=="")
        {
            tb.current.disabled = true
            //btn.current.disabled = true
            setIsWaiting(true)
            const hs =  [
                // {
                //     role: "user",
                //     parts: [
                //         { text: window.desc },
                //         { text: "In addition to the text provided in the 0th part. There are other documents for review. The links to the documents are Here. Please analyze them as well."},
                //         { text: `Document Links:\n${window.pdfUrls.join("\n")}`}
                //     ],
                // },
                // {
                //     role: "model",
                //     parts: [{ text: "ok" }],
                // },
                ]
            for(let i=0;i<chatData.length;i++){
                hs.push({role:"user",parts:[{text:chatData[i].q}]})
                hs.push({role:"model",parts:[{text:chatData[i].a}]})
            }
            if(!chatS){
                const chat = model.startChat({
                    history:hs,
                    generationConfig: {
                    temperature:0,
                    topP:0,
                    topK:1,
                    maxOutputTokens: 1024,
                    },});
                setChatS(chat);
                addQuestion(mainCollection,tb.current.value,setChatData,setIsWaiting,setIsConnecting,setMainCollection,chat,chatData)
            }
            else{
                addQuestion(mainCollection,tb.current.value,setChatData,setIsWaiting,setIsConnecting,setMainCollection,chatS,chatData)
            }
            
            
            tb.current.value = ""
        }
    }
    return(
        <>
        <div className="d-flex bluebg p-2 align-items-center">
            <input type="text" ref={tb} style={{"width":"100%"}} className="me-auto d-block" disabled = {isWaiting} onKeyUp={handleKeyPress} />
            
            <div className="ms-2 me-0" style={{"cursor":"pointer"}} onClick={handleClick}>
                <svg width="36px" height="36px" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg" fill="#228b22">
                    <g id="SVGRepo_iconCarrier"> 
                        <path d="M0 0h48v48H0z" fill="none"/> 
                        <g id="Shopicon"> 
                            <path d="M44,24L3.999,3.999l6,20.001L4,44L44,24z M31.056,26L10.491,36.282L13.575,26H31.056z M31.055,22h-17.48L10.49,11.717 L31.055,22z"/> 
                        </g> 
                    </g>
                </svg>
                {/* <button ref={btn} onClick={handleClick} disabled = {isWaiting}>Ask Me!</button> */}
            </div>
        </div>
        {!is_app && <div className="text-center ft-txt p-1">Powered by <a href="https://www.perklist.com/" target="_blank">Perklist</a></div>}
        </>
    )
}