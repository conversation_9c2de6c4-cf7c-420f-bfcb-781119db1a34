import { useEffect, useRef } from "react"

export default function DisplayArea({chatData,isWaiting,isConnecting}){
    const l_div = useRef();
    const queryParams = new URLSearchParams(window.location.search);
    const is_app = queryParams.get("app")
    useEffect(()=>{
        l_div.current.scrollIntoView({ behavior: "smooth" });
    },[chatData,isWaiting])
    return(
        <div className={"pt-1 "+(is_app?"content-iframe-app":"content-iframe")}>
            <div className="clearfix">
                <div className="float-start mb-2" style={{"maxWidth":"80%"}} >
                {window.bn_h.toLowerCase()!=="true" && <div className="mb-2 asst"><strong>{window.bn}</strong></div>}
                    <div>{window.st_msg}</div>
                </div>
            </div>
            {
        chatData.map((msg)=>{
            return(
            <div className="clearfix" key={msg.id}>
                <div className="float-end w-80 p-2 ps-3 pe-3 me-2 user_box mb-1" style={{"maxWidth":"80%"}}>{msg.q}</div>
                <div className="clearfix">&nbsp;</div>
                {
                    msg.a &&
                <div className="float-start mb-2 mt-2" style={{"maxWidth":"80%"}} >
                    {window.bn_h.toLowerCase()!=="true" && <div className="mb-2 asst"><strong>{window.bn}</strong></div>}
                    <div className="htm_class" dangerouslySetInnerHTML={{__html: msg.a}}></div>
                </div>
                }
            </div>
            )
        })
        }
        {isWaiting && <div className="text-center mt-3 mb-3">{isConnecting?"Typing...":"Typing..."}</div>}
        <div ref={l_div}></div>
        </div>
    )
}