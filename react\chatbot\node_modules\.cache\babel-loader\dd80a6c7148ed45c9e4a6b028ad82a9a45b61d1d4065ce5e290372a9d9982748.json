{"ast": null, "code": "import{initializeApp}from\"firebase/app\";import{getAI,getGenerativeModel}from\"firebase/ai\";import{getAuth}from\"firebase/auth\";import{getFirestore,collection,doc,query,where,getDocs,onSnapshot,limit,orderBy,addDoc,serverTimestamp,updateDoc}from\"firebase/firestore\";import*as pdfjsLib from\"pdfjs-dist\";import pdfWorker from\"pdfjs-dist/build/pdf.worker.entry\";// Set the worker source dynamically\npdfjsLib.GlobalWorkerOptions.workerSrc=pdfWorker;async function extractTextFromPdfUrl(pdfUrl){try{// Fetch the PDF file\nconst response=await fetch(pdfUrl);if(!response.ok)throw new Error(\"Failed to fetch PDF\");// Convert response to an ArrayBuffer (needed for PDF.js)\nconst pdfBuffer=await response.arrayBuffer();// Load the PDF document\nconst pdf=await pdfjsLib.getDocument(pdfBuffer).promise;let extractedText=\"\";// Loop through each page and extract text\nfor(let i=1;i<=pdf.numPages;i++){const page=await pdf.getPage(i);const textContent=await page.getTextContent();const text=textContent.items.map(item=>item.str).join(\" \");extractedText+=text+\"\\n\\n\";// Separate pages with line breaks\n}return extractedText;}catch(error){console.error(\"Error extracting text from PDF:\",error);return null;}}const firebaseConfig=window.config;const firebaseApp=initializeApp(firebaseConfig);const vertexAI=getVertexAI(firebaseApp);const p=[{text:window.desc}];window.showLoader();for(let i=0;i<window.pdfUrls.length;i++){const txt=await extractTextFromPdfUrl(window.pdfUrls[i]);p.push({text:txt});}window.hideLoader();const systemInstruction={role:\"system\",parts:p};const model=getGenerativeModel(vertexAI,{model:\"gemini-2.0-flash\",systemInstruction:systemInstruction});//const model = genAI.getGenerativeModel({ model: \"gemini-2.0-flash-exp\" });\nconst auth=getAuth(firebaseApp);const db=getFirestore();const setSnapShot=async(chatbotId,setChatData,setIsWaiting)=>{const placeDocRef=doc(db,\"n_chatbot\",chatbotId);const qry1=query(collection(placeDocRef,\"messages\"),orderBy(\"q_dt\"));onSnapshot(qry1,snapshot=>{const chat_data=[];for(const q of snapshot.docs){//console.log(q.data())\nconst data=q.data();data['id']=q.id;chat_data.push(data);}const last_msg=chat_data[chat_data.length-1];if(last_msg.a){setIsWaiting(false);}else{setIsWaiting(true);}setChatData(chat_data);});};const chatSnapShot=async(setChatData,setMainCollection,setIsWaiting)=>{const qry=query(collection(db,\"n_chatbot\"),where(\"place_id\",\"==\",window.pd),where(\"session_id\",\"==\",window.s_d),limit(1));const chatSnap=await getDocs(qry);chatSnap.forEach(doc1=>{const placeDocRef=doc(db,\"n_chatbot\",doc1.id);setMainCollection(placeDocRef);setSnapShot(doc1.id,setChatData,setIsWaiting);});};const addQuestion=async(mainCollection,question,setChatData,setIsWaiting,setIsConnecting,setMainCollection,chatS,chatData)=>{try{let collectionRef;let passColl=mainCollection;if(mainCollection){setIsConnecting(false);collectionRef=collection(mainCollection,'messages');}else{setIsConnecting(true);const search_array=window.pn.split(\" \");search_array.push(window.pd);const mPayload={\"place_id\":window.pd,\"session_id\":window.s_d,\"date_created\":serverTimestamp(),\"place_name\":window.pn,\"prompt_tokens\":0,\"ans_tokens\":0,\"total_tokens\":0,\"search_terms\":search_array,\"first_msg\":question};const collectionRef1=collection(db,\"n_chatbot\");const{id}=await addDoc(collectionRef1,mPayload);const placeDocRef=doc(db,\"n_chatbot\",id);setMainCollection(placeDocRef);passColl=placeDocRef;collectionRef=collection(placeDocRef,'messages');setSnapShot(id,setChatData,setIsWaiting);}const payload={\"q\":question,\"q_dt\":serverTimestamp()};const chatInput=question;const docRef=await addDoc(collectionRef,payload);setIsConnecting(false);try{if(chatData.length<=window.mm){const result=await chatS.sendMessage(chatInput);const response=await result.response;let resText=response.candidates[0].content.parts[0].text;resText=resText.replace('```html','').replace('```','').replace('\\r','').replace('\\n','');//console.log(resText)\nawait updateDoc(docRef,{\"a\":sanitizeForFirestore(resText),\"a_dt\":serverTimestamp()});//await addDoc(collectionRef, {\"a\":response.candidates[0].content.parts[0].text,\"a_dt\":serverTimestamp()});\n//console.log('aggregated response: ', JSON.stringify(response.usageMetadata));\nconst data={chatbot_id:passColl.id,msg_id:docRef.id,place_id:window.pd,q_token:response.usageMetadata.promptTokenCount,a_token:response.usageMetadata.candidatesTokenCount,t_token:response.usageMetadata.totalTokenCount};fetch(window.pURL+\"api/v2/update_tokens\",{method:\"POST\",mode:\"cors\",cache:\"no-cache\",headers:{\"Content-Type\":\"application/json\"},redirect:\"follow\",referrerPolicy:\"no-referrer\",body:JSON.stringify(data)});}else{await updateDoc(docRef,{\"a\":\"I am sorry, but I can’t answer more questions on this topic. Please contact the business for further questions.\",\"a_dt\":serverTimestamp()});setIsWaiting(false);setIsConnecting(false);}}catch(e){console.log(e);setIsWaiting(false);//updateDoc(docRef,{\"a\":\"Sorry! This question is not in my scope\",\"a_dt\":serverTimestamp()})\n}}catch(e){console.log(e);setIsWaiting(false);setIsConnecting(false);//updateDoc(docRef,{\"a\":\"Sorry! This question is not in my scope\",\"a_dt\":serverTimestamp()})\n}};function sanitizeForFirestore(value){if(typeof value===\"number\"){return value;// Return numbers as-is\n}if(typeof value!==\"string\"){try{value=JSON.stringify(value);// Convert objects/arrays to JSON-safe strings\n}catch(e){return\"\";// Return empty string if serialization fails\n}}// Remove all non-printable characters (ASCII control chars + Unicode invisible chars)\nreturn value.replace(/[\\x00-\\x1F\\x7F-\\x9F\\u200B\\u200C\\u200D\\uFEFF]/g,\"\");}export{chatSnapShot,addQuestion,model};", "map": {"version": 3, "names": ["initializeApp", "getAI", "getGenerativeModel", "getAuth", "getFirestore", "collection", "doc", "query", "where", "getDocs", "onSnapshot", "limit", "orderBy", "addDoc", "serverTimestamp", "updateDoc", "pdfjsLib", "pdfWorker", "GlobalWorkerOptions", "workerSrc", "extractTextFromPdfUrl", "pdfUrl", "response", "fetch", "ok", "Error", "pdfBuffer", "arrayBuffer", "pdf", "getDocument", "promise", "extractedText", "i", "numPages", "page", "getPage", "textContent", "getTextContent", "text", "items", "map", "item", "str", "join", "error", "console", "firebaseConfig", "window", "config", "firebaseApp", "vertexAI", "getVertexAI", "p", "desc", "<PERSON><PERSON><PERSON><PERSON>", "pdfUrls", "length", "txt", "push", "<PERSON><PERSON><PERSON><PERSON>", "systemInstruction", "role", "parts", "model", "auth", "db", "setSnapShot", "chatbotId", "setChatData", "setIsWaiting", "placeDocRef", "qry1", "snapshot", "chat_data", "q", "docs", "data", "id", "last_msg", "a", "chatSnapShot", "setMainCollection", "qry", "pd", "s_d", "chatSnap", "for<PERSON>ach", "doc1", "addQuestion", "mainCollection", "question", "setIsConnecting", "chatS", "chatData", "collectionRef", "passColl", "search_array", "pn", "split", "mPayload", "collectionRef1", "payload", "chatInput", "doc<PERSON>ef", "mm", "result", "sendMessage", "resText", "candidates", "content", "replace", "sanitizeForFirestore", "chatbot_id", "msg_id", "place_id", "q_token", "usageMetadata", "promptTokenCount", "a_token", "candidatesTokenCount", "t_token", "totalTokenCount", "pURL", "method", "mode", "cache", "headers", "redirect", "referrerPolicy", "body", "JSON", "stringify", "e", "log", "value"], "sources": ["c:/custom/projects/vipdigs/react/chatbot/src/firebase.js"], "sourcesContent": ["import { initializeApp } from \"firebase/app\";\r\nimport { getAI, getGenerativeModel } from \"firebase/ai\";\r\nimport { getAuth } from \"firebase/auth\";\r\nimport {\r\n    getFirestore, \r\n    collection,\r\n    doc,\r\n    query,\r\n    where,\r\n    getDocs,\r\n    onSnapshot,\r\n    limit,\r\n    orderBy,\r\n    addDoc,\r\n    serverTimestamp,\r\n    updateDoc\r\n} from \"firebase/firestore\";\r\nimport * as pdfjsLib from \"pdfjs-dist\";\r\nimport pdfWorker from \"pdfjs-dist/build/pdf.worker.entry\";\r\n\r\n// Set the worker source dynamically\r\npdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker;\r\n\r\nasync function extractTextFromPdfUrl(pdfUrl) {\r\n    try {\r\n      // Fetch the PDF file\r\n      const response = await fetch(pdfUrl);\r\n      if (!response.ok) throw new Error(\"Failed to fetch PDF\");\r\n  \r\n      // Convert response to an ArrayBuffer (needed for PDF.js)\r\n      const pdfBuffer = await response.arrayBuffer();\r\n  \r\n      // Load the PDF document\r\n      const pdf = await pdfjsLib.getDocument(pdfBuffer).promise;\r\n      let extractedText = \"\";\r\n  \r\n      // Loop through each page and extract text\r\n      for (let i = 1; i <= pdf.numPages; i++) {\r\n        const page = await pdf.getPage(i);\r\n        const textContent = await page.getTextContent();\r\n        const text = textContent.items.map((item) => item.str).join(\" \");\r\n        extractedText += text + \"\\n\\n\"; // Separate pages with line breaks\r\n      }\r\n  \r\n      return extractedText;\r\n    } catch (error) {\r\n      console.error(\"Error extracting text from PDF:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n\r\nconst firebaseConfig = window.config;\r\n\r\nconst firebaseApp = initializeApp(firebaseConfig);\r\nconst vertexAI = getVertexAI(firebaseApp);\r\n\r\nconst p = [{ text: window.desc }];\r\nwindow.showLoader();\r\nfor(let i=0;i<window.pdfUrls.length;i++){\r\n   const txt = await extractTextFromPdfUrl(window.pdfUrls[i]);\r\n   p.push({text: txt}); \r\n}\r\nwindow.hideLoader();\r\nconst systemInstruction = {\r\n    role: \"system\",\r\n    parts: p\r\n  };\r\n\r\nconst model = getGenerativeModel(vertexAI, { model: \"gemini-2.0-flash\",\r\n    systemInstruction:systemInstruction });\r\n//const model = genAI.getGenerativeModel({ model: \"gemini-2.0-flash-exp\" });\r\n\r\nconst auth = getAuth(firebaseApp);\r\nconst db = getFirestore();\r\n\r\nconst setSnapShot = async(chatbotId,setChatData,setIsWaiting)=>{\r\n    const placeDocRef = doc(db,\"n_chatbot\",chatbotId);\r\n    const qry1 = query(collection(placeDocRef,\"messages\"),orderBy(\"q_dt\"))\r\n    onSnapshot(qry1,(snapshot)=>{\r\n        const chat_data = [];\r\n        for (const q of snapshot.docs){\r\n            //console.log(q.data())\r\n            const data = q.data();\r\n            data['id'] = q.id;\r\n            chat_data.push(data)\r\n        }\r\n        const last_msg = chat_data[chat_data.length-1];\r\n        if(last_msg.a){\r\n            setIsWaiting(false)\r\n        }\r\n        else{\r\n            setIsWaiting(true)\r\n        }\r\n        setChatData(chat_data)\r\n    })\r\n}\r\nconst chatSnapShot = async (setChatData,setMainCollection,setIsWaiting)=>{\r\n    const qry  = query(collection(db,\"n_chatbot\"),where(\"place_id\",\"==\",window.pd),where(\"session_id\",\"==\",window.s_d),limit(1))\r\n    const chatSnap = await getDocs(qry);\r\n    chatSnap.forEach((doc1) => {\r\n        const placeDocRef = doc(db,\"n_chatbot\",doc1.id);\r\n        setMainCollection(placeDocRef);\r\n        setSnapShot(doc1.id,setChatData,setIsWaiting)\r\n    })\r\n}\r\n\r\nconst addQuestion = async (mainCollection,question,setChatData,setIsWaiting,setIsConnecting,setMainCollection,chatS,chatData) =>{\r\n    try{\r\n        let collectionRef;\r\n        let passColl = mainCollection;\r\n        if (mainCollection){\r\n            setIsConnecting(false)\r\n            collectionRef = collection(mainCollection,'messages');\r\n        }\r\n        else{\r\n            setIsConnecting(true);\r\n            const search_array = window.pn.split(\" \");\r\n            search_array.push(window.pd)\r\n            const mPayload = {\r\n                \"place_id\":window.pd,\r\n                \"session_id\":window.s_d,\r\n                \"date_created\":serverTimestamp(),\r\n                \"place_name\":window.pn,\r\n                \"prompt_tokens\":0,\r\n                \"ans_tokens\":0,\r\n                \"total_tokens\":0,\r\n                \"search_terms\":search_array,\r\n                \"first_msg\":question\r\n            }\r\n            const collectionRef1 = collection(db,\"n_chatbot\")\r\n            const {id} = await addDoc(collectionRef1, mPayload);\r\n            const placeDocRef = doc(db,\"n_chatbot\",id);\r\n            setMainCollection(placeDocRef);\r\n            passColl = placeDocRef;\r\n            collectionRef = collection(placeDocRef,'messages');\r\n            setSnapShot(id,setChatData,setIsWaiting)\r\n        }\r\n        const payload = {\"q\":question,\"q_dt\":serverTimestamp()}\r\n    \r\n        const chatInput = question;\r\n        const docRef = await addDoc(collectionRef, payload);\r\n        setIsConnecting(false);\r\n        try{\r\n            if(chatData.length<=window.mm)\r\n            {\r\n                const result = await chatS.sendMessage(chatInput);\r\n                const response = await result.response;\r\n                let resText = response.candidates[0].content.parts[0].text;\r\n                resText = resText.replace('```html','').replace('```','').replace('\\r','').replace('\\n','')\r\n                //console.log(resText)\r\n                await updateDoc(docRef,{\"a\":sanitizeForFirestore(resText),\"a_dt\":serverTimestamp()})\r\n                //await addDoc(collectionRef, {\"a\":response.candidates[0].content.parts[0].text,\"a_dt\":serverTimestamp()});\r\n                //console.log('aggregated response: ', JSON.stringify(response.usageMetadata));\r\n                const data = {\r\n                    chatbot_id:passColl.id,\r\n                    msg_id:docRef.id,\r\n                    place_id:window.pd,\r\n                    q_token:response.usageMetadata.promptTokenCount,\r\n                    a_token:response.usageMetadata.candidatesTokenCount,\r\n                    t_token:response.usageMetadata.totalTokenCount,\r\n                }\r\n                fetch(window.pURL+\"api/v2/update_tokens\", {\r\n                    method: \"POST\",\r\n                    mode: \"cors\",\r\n                    cache: \"no-cache\",\r\n                    headers: { \"Content-Type\": \"application/json\" },\r\n                    redirect: \"follow\",\r\n                    referrerPolicy: \"no-referrer\",\r\n                    body: JSON.stringify(data),\r\n                });\r\n            }\r\n            else{\r\n                await updateDoc(docRef,{\"a\":\"I am sorry, but I can’t answer more questions on this topic. Please contact the business for further questions.\",\"a_dt\":serverTimestamp()})\r\n                setIsWaiting(false)\r\n                setIsConnecting(false);\r\n                \r\n            }\r\n        }\r\n        catch(e){\r\n            console.log(e)\r\n            setIsWaiting(false);\r\n            //updateDoc(docRef,{\"a\":\"Sorry! This question is not in my scope\",\"a_dt\":serverTimestamp()})\r\n        }\r\n        \r\n    }\r\n    catch(e){\r\n        console.log(e)\r\n        setIsWaiting(false);\r\n        setIsConnecting(false);\r\n\r\n        //updateDoc(docRef,{\"a\":\"Sorry! This question is not in my scope\",\"a_dt\":serverTimestamp()})\r\n    }\r\n\r\n}\r\nfunction sanitizeForFirestore(value) {\r\n    if (typeof value === \"number\") {\r\n        return value; // Return numbers as-is\r\n    }\r\n\r\n    if (typeof value !== \"string\") {\r\n        try {\r\n            value = JSON.stringify(value); // Convert objects/arrays to JSON-safe strings\r\n        } catch (e) {\r\n            return \"\"; // Return empty string if serialization fails\r\n        }\r\n    }\r\n\r\n    // Remove all non-printable characters (ASCII control chars + Unicode invisible chars)\r\n    return value.replace(/[\\x00-\\x1F\\x7F-\\x9F\\u200B\\u200C\\u200D\\uFEFF]/g, \"\");\r\n}\r\nexport{chatSnapShot,addQuestion,model}\r\n"], "mappings": "AAAA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,KAAK,CAAEC,kBAAkB,KAAQ,aAAa,CACvD,OAASC,OAAO,KAAQ,eAAe,CACvC,OACIC,YAAY,CACZC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,CACNC,eAAe,CACfC,SAAS,KACN,oBAAoB,CAC3B,MAAO,GAAK,CAAAC,QAAQ,KAAM,YAAY,CACtC,MAAO,CAAAC,SAAS,KAAM,mCAAmC,CAEzD;AACAD,QAAQ,CAACE,mBAAmB,CAACC,SAAS,CAAGF,SAAS,CAElD,cAAe,CAAAG,qBAAqBA,CAACC,MAAM,CAAE,CACzC,GAAI,CACF;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACF,MAAM,CAAC,CACpC,GAAI,CAACC,QAAQ,CAACE,EAAE,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,qBAAqB,CAAC,CAExD;AACA,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAJ,QAAQ,CAACK,WAAW,CAAC,CAAC,CAE9C;AACA,KAAM,CAAAC,GAAG,CAAG,KAAM,CAAAZ,QAAQ,CAACa,WAAW,CAACH,SAAS,CAAC,CAACI,OAAO,CACzD,GAAI,CAAAC,aAAa,CAAG,EAAE,CAEtB;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIJ,GAAG,CAACK,QAAQ,CAAED,CAAC,EAAE,CAAE,CACtC,KAAM,CAAAE,IAAI,CAAG,KAAM,CAAAN,GAAG,CAACO,OAAO,CAACH,CAAC,CAAC,CACjC,KAAM,CAAAI,WAAW,CAAG,KAAM,CAAAF,IAAI,CAACG,cAAc,CAAC,CAAC,CAC/C,KAAM,CAAAC,IAAI,CAAGF,WAAW,CAACG,KAAK,CAACC,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAChEZ,aAAa,EAAIO,IAAI,CAAG,MAAM,CAAE;AAClC,CAEA,MAAO,CAAAP,aAAa,CACtB,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,MAAO,KAAI,CACb,CACF,CAGF,KAAM,CAAAE,cAAc,CAAGC,MAAM,CAACC,MAAM,CAEpC,KAAM,CAAAC,WAAW,CAAGjD,aAAa,CAAC8C,cAAc,CAAC,CACjD,KAAM,CAAAI,QAAQ,CAAGC,WAAW,CAACF,WAAW,CAAC,CAEzC,KAAM,CAAAG,CAAC,CAAG,CAAC,CAAEd,IAAI,CAAES,MAAM,CAACM,IAAK,CAAC,CAAC,CACjCN,MAAM,CAACO,UAAU,CAAC,CAAC,CACnB,IAAI,GAAI,CAAAtB,CAAC,CAAC,CAAC,CAACA,CAAC,CAACe,MAAM,CAACQ,OAAO,CAACC,MAAM,CAACxB,CAAC,EAAE,CAAC,CACrC,KAAM,CAAAyB,GAAG,CAAG,KAAM,CAAArC,qBAAqB,CAAC2B,MAAM,CAACQ,OAAO,CAACvB,CAAC,CAAC,CAAC,CAC1DoB,CAAC,CAACM,IAAI,CAAC,CAACpB,IAAI,CAAEmB,GAAG,CAAC,CAAC,CACtB,CACAV,MAAM,CAACY,UAAU,CAAC,CAAC,CACnB,KAAM,CAAAC,iBAAiB,CAAG,CACtBC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAEV,CACT,CAAC,CAEH,KAAM,CAAAW,KAAK,CAAG7D,kBAAkB,CAACgD,QAAQ,CAAE,CAAEa,KAAK,CAAE,kBAAkB,CAClEH,iBAAiB,CAACA,iBAAkB,CAAC,CAAC,CAC1C;AAEA,KAAM,CAAAI,IAAI,CAAG7D,OAAO,CAAC8C,WAAW,CAAC,CACjC,KAAM,CAAAgB,EAAE,CAAG7D,YAAY,CAAC,CAAC,CAEzB,KAAM,CAAA8D,WAAW,CAAG,KAAAA,CAAMC,SAAS,CAACC,WAAW,CAACC,YAAY,GAAG,CAC3D,KAAM,CAAAC,WAAW,CAAGhE,GAAG,CAAC2D,EAAE,CAAC,WAAW,CAACE,SAAS,CAAC,CACjD,KAAM,CAAAI,IAAI,CAAGhE,KAAK,CAACF,UAAU,CAACiE,WAAW,CAAC,UAAU,CAAC,CAAC1D,OAAO,CAAC,MAAM,CAAC,CAAC,CACtEF,UAAU,CAAC6D,IAAI,CAAEC,QAAQ,EAAG,CACxB,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,IAAK,KAAM,CAAAC,CAAC,GAAI,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAC1B;AACA,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAACE,IAAI,CAAC,CAAC,CACrBA,IAAI,CAAC,IAAI,CAAC,CAAGF,CAAC,CAACG,EAAE,CACjBJ,SAAS,CAACf,IAAI,CAACkB,IAAI,CAAC,CACxB,CACA,KAAM,CAAAE,QAAQ,CAAGL,SAAS,CAACA,SAAS,CAACjB,MAAM,CAAC,CAAC,CAAC,CAC9C,GAAGsB,QAAQ,CAACC,CAAC,CAAC,CACVV,YAAY,CAAC,KAAK,CAAC,CACvB,CAAC,IACG,CACAA,YAAY,CAAC,IAAI,CAAC,CACtB,CACAD,WAAW,CAACK,SAAS,CAAC,CAC1B,CAAC,CAAC,CACN,CAAC,CACD,KAAM,CAAAO,YAAY,CAAG,KAAAA,CAAOZ,WAAW,CAACa,iBAAiB,CAACZ,YAAY,GAAG,CACrE,KAAM,CAAAa,GAAG,CAAI3E,KAAK,CAACF,UAAU,CAAC4D,EAAE,CAAC,WAAW,CAAC,CAACzD,KAAK,CAAC,UAAU,CAAC,IAAI,CAACuC,MAAM,CAACoC,EAAE,CAAC,CAAC3E,KAAK,CAAC,YAAY,CAAC,IAAI,CAACuC,MAAM,CAACqC,GAAG,CAAC,CAACzE,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5H,KAAM,CAAA0E,QAAQ,CAAG,KAAM,CAAA5E,OAAO,CAACyE,GAAG,CAAC,CACnCG,QAAQ,CAACC,OAAO,CAAEC,IAAI,EAAK,CACvB,KAAM,CAAAjB,WAAW,CAAGhE,GAAG,CAAC2D,EAAE,CAAC,WAAW,CAACsB,IAAI,CAACV,EAAE,CAAC,CAC/CI,iBAAiB,CAACX,WAAW,CAAC,CAC9BJ,WAAW,CAACqB,IAAI,CAACV,EAAE,CAACT,WAAW,CAACC,YAAY,CAAC,CACjD,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAOC,cAAc,CAACC,QAAQ,CAACtB,WAAW,CAACC,YAAY,CAACsB,eAAe,CAACV,iBAAiB,CAACW,KAAK,CAACC,QAAQ,GAAI,CAC5H,GAAG,CACC,GAAI,CAAAC,aAAa,CACjB,GAAI,CAAAC,QAAQ,CAAGN,cAAc,CAC7B,GAAIA,cAAc,CAAC,CACfE,eAAe,CAAC,KAAK,CAAC,CACtBG,aAAa,CAAGzF,UAAU,CAACoF,cAAc,CAAC,UAAU,CAAC,CACzD,CAAC,IACG,CACAE,eAAe,CAAC,IAAI,CAAC,CACrB,KAAM,CAAAK,YAAY,CAAGjD,MAAM,CAACkD,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CACzCF,YAAY,CAACtC,IAAI,CAACX,MAAM,CAACoC,EAAE,CAAC,CAC5B,KAAM,CAAAgB,QAAQ,CAAG,CACb,UAAU,CAACpD,MAAM,CAACoC,EAAE,CACpB,YAAY,CAACpC,MAAM,CAACqC,GAAG,CACvB,cAAc,CAACtE,eAAe,CAAC,CAAC,CAChC,YAAY,CAACiC,MAAM,CAACkD,EAAE,CACtB,eAAe,CAAC,CAAC,CACjB,YAAY,CAAC,CAAC,CACd,cAAc,CAAC,CAAC,CAChB,cAAc,CAACD,YAAY,CAC3B,WAAW,CAACN,QAChB,CAAC,CACD,KAAM,CAAAU,cAAc,CAAG/F,UAAU,CAAC4D,EAAE,CAAC,WAAW,CAAC,CACjD,KAAM,CAACY,EAAE,CAAC,CAAG,KAAM,CAAAhE,MAAM,CAACuF,cAAc,CAAED,QAAQ,CAAC,CACnD,KAAM,CAAA7B,WAAW,CAAGhE,GAAG,CAAC2D,EAAE,CAAC,WAAW,CAACY,EAAE,CAAC,CAC1CI,iBAAiB,CAACX,WAAW,CAAC,CAC9ByB,QAAQ,CAAGzB,WAAW,CACtBwB,aAAa,CAAGzF,UAAU,CAACiE,WAAW,CAAC,UAAU,CAAC,CAClDJ,WAAW,CAACW,EAAE,CAACT,WAAW,CAACC,YAAY,CAAC,CAC5C,CACA,KAAM,CAAAgC,OAAO,CAAG,CAAC,GAAG,CAACX,QAAQ,CAAC,MAAM,CAAC5E,eAAe,CAAC,CAAC,CAAC,CAEvD,KAAM,CAAAwF,SAAS,CAAGZ,QAAQ,CAC1B,KAAM,CAAAa,MAAM,CAAG,KAAM,CAAA1F,MAAM,CAACiF,aAAa,CAAEO,OAAO,CAAC,CACnDV,eAAe,CAAC,KAAK,CAAC,CACtB,GAAG,CACC,GAAGE,QAAQ,CAACrC,MAAM,EAAET,MAAM,CAACyD,EAAE,CAC7B,CACI,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAb,KAAK,CAACc,WAAW,CAACJ,SAAS,CAAC,CACjD,KAAM,CAAAhF,QAAQ,CAAG,KAAM,CAAAmF,MAAM,CAACnF,QAAQ,CACtC,GAAI,CAAAqF,OAAO,CAAGrF,QAAQ,CAACsF,UAAU,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC/C,KAAK,CAAC,CAAC,CAAC,CAACxB,IAAI,CAC1DqE,OAAO,CAAGA,OAAO,CAACG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAC3F;AACA,KAAM,CAAA/F,SAAS,CAACwF,MAAM,CAAC,CAAC,GAAG,CAACQ,oBAAoB,CAACJ,OAAO,CAAC,CAAC,MAAM,CAAC7F,eAAe,CAAC,CAAC,CAAC,CAAC,CACpF;AACA;AACA,KAAM,CAAA8D,IAAI,CAAG,CACToC,UAAU,CAACjB,QAAQ,CAAClB,EAAE,CACtBoC,MAAM,CAACV,MAAM,CAAC1B,EAAE,CAChBqC,QAAQ,CAACnE,MAAM,CAACoC,EAAE,CAClBgC,OAAO,CAAC7F,QAAQ,CAAC8F,aAAa,CAACC,gBAAgB,CAC/CC,OAAO,CAAChG,QAAQ,CAAC8F,aAAa,CAACG,oBAAoB,CACnDC,OAAO,CAAClG,QAAQ,CAAC8F,aAAa,CAACK,eACnC,CAAC,CACDlG,KAAK,CAACwB,MAAM,CAAC2E,IAAI,CAAC,sBAAsB,CAAE,CACtCC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,UAAU,CACjBC,OAAO,CAAE,CAAE,cAAc,CAAE,kBAAmB,CAAC,CAC/CC,QAAQ,CAAE,QAAQ,CAClBC,cAAc,CAAE,aAAa,CAC7BC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACvD,IAAI,CAC7B,CAAC,CAAC,CACN,CAAC,IACG,CACA,KAAM,CAAA7D,SAAS,CAACwF,MAAM,CAAC,CAAC,GAAG,CAAC,iHAAiH,CAAC,MAAM,CAACzF,eAAe,CAAC,CAAC,CAAC,CAAC,CACxKuD,YAAY,CAAC,KAAK,CAAC,CACnBsB,eAAe,CAAC,KAAK,CAAC,CAE1B,CACJ,CACA,MAAMyC,CAAC,CAAC,CACJvF,OAAO,CAACwF,GAAG,CAACD,CAAC,CAAC,CACd/D,YAAY,CAAC,KAAK,CAAC,CACnB;AACJ,CAEJ,CACA,MAAM+D,CAAC,CAAC,CACJvF,OAAO,CAACwF,GAAG,CAACD,CAAC,CAAC,CACd/D,YAAY,CAAC,KAAK,CAAC,CACnBsB,eAAe,CAAC,KAAK,CAAC,CAEtB;AACJ,CAEJ,CAAC,CACD,QAAS,CAAAoB,oBAAoBA,CAACuB,KAAK,CAAE,CACjC,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC3B,MAAO,CAAAA,KAAK,CAAE;AAClB,CAEA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC3B,GAAI,CACAA,KAAK,CAAGJ,IAAI,CAACC,SAAS,CAACG,KAAK,CAAC,CAAE;AACnC,CAAE,MAAOF,CAAC,CAAE,CACR,MAAO,EAAE,CAAE;AACf,CACJ,CAEA;AACA,MAAO,CAAAE,KAAK,CAACxB,OAAO,CAAC,+CAA+C,CAAE,EAAE,CAAC,CAC7E,CACA,OAAO9B,YAAY,CAACQ,WAAW,CAACzB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}