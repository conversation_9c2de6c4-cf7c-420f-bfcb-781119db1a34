.bluebg {
  background-color: lightgrey
}

#root{
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  font-size: 16px!important;
  overflow: hidden;
}
.ftr {
  position: relative;
  bottom: 0;
  width: 100%;
  
}
.ft-txt{
  font-size: 12px;
  background-color: #f0edf0;
}
.htm_class:first-child{
  margin: 0px;
}
.htm_class p, .htm_class ul{
  margin-bottom: 0px;
}
.content-iframe{
  height: calc(100dvh - 88px);
  overflow: auto;
  scrollbar-width: thin;
}
.content-iframe-app{
  height: calc(100dvh - 60px);
  overflow: auto;
  scrollbar-width: thin;
}
.user_box{
  background-color: rgba(211, 211, 211, 0.274);
  border-radius: 20px;
}
.asst{
  font-size: 16px;
  color: forestgreen;
}
input, input:disabled{
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  font-size: 14px;
  border-radius: 30px;
  padding: 7px;
  border: 1px solid gray;
  background-color: white;
}
button{
  border: 1px solid gray;
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  font-size: 14px;
  border-radius: 10px;
  padding: 7px;
  white-space: nowrap;
  background-color: forestgreen;
  color: white;
}
.welcome{
  background-color: grey;
  color: white;
  display: inline-block;
  margin: 0 auto;
  font-size: 11px;
  padding: 2px;
  padding-left: 10px;
  padding-right: 10px;
  
}