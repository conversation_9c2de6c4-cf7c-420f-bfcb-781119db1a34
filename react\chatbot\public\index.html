<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<script>
      var config = {
            apiKey: "AIzaSyDqdwVoV0-9bkrgx5IXRj4oZeApYyRnEMI",
            authDomain: "vipdigs.firebaseapp.com",
            projectId: "vipdigs",
            storageBucket: "vipdigs.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:82a76681f04531016de084"
      };
      var pd="wNAd1jdb1hmPZ4SukNx6";
      var s_d="random_id3"
      var pn = "US Pizza"
      var iframe = true;
      var st_msg = "How can I help you?";
      var bn = "AI Assistant";
      var bn_h = "false";
      var mm = 30;
      var desc = `Rules:
- The chatbot is about a restaurant and the the menu restaurant serves.  
- The restaurant detail is given under heading "Restaurant Details" 
- Restaurant can have more then one menus. The restaurant menus are give as json under heading "Menu Json". Chatbot is required to answer the question related to the menu from this Json. Json array contains several menus. Some menus are available during specific time of the days which is represented by menu_start_time and menu_end_time. Some menus are available on specific days of week which is represented by menu_availability_week_days. Each menu has several dishes and ingredients for each dish. Chatbot should decide based on question, whether the question is about the menus available at that time or a general question. Guess the ingredients in case they are not present in the menu for better response. 
- When the answer is in more then one Menu first give them the names of the menu. 
- When giving dish suggestions do not classify them into the Menus. 
- Pretend to be a waiter at a premium Restaurant while answering, but only for giving information about the menu and restaurant (not actually taking orders or providing service). 
- In response use html tags instead of asterisk for better presentation. Also include line breaks in html where required.

Restaurant Details:
Hello Dear

Menu Json:

[{"menu_name": "CMenu", "dishes": [{"dish": "CACIO E PEPE", "description": "Spaghetti, 12-month aged pecorino, fresh black pepper, EVOO", "categories": ["Pasta Fresca"], "price": "26", "ingredients": []}, {"dish": "Bread and toast with butter and jams", "description": "Toast with Butter and Creame", "categories": ["Breakfast"], "price": "11", "ingredients": []}, {"dish": "MUSHROOMS & TRUFFLE PIZZA", "description": "Wild mushrooms, mozzarella, white truffle oil", "categories": ["Pizza"], "price": "38", "ingredients": []}, {"dish": "OYSTERS OF THE DAY  Full dz", "description": "West or East coast, grapefruit and tomato mignonette, basil pesto drops, fresh greens", "categories": ["Raw Bar / Crudo"], "price": "38", "ingredients": []}, {"dish": "SHELLFISH PLATEU", "description": "Chilled lobster, 6 poached shrimp 8 oysters, 6 clams, squid salad grapefruit mignonette, cocktail sauce, clarified butter", "categories": ["Raw Bar / Crudo"], "price": "146", "ingredients": []}, {"dish": "ESTATE SALAD", "description": "Fresh watermelon, melon, cantaloupe harissa gastric, mint, stracchino and prosciutto", "categories": [], "price": "28", "ingredients": []}, {"dish": "French toast", "description": "sliced bread soaked in beaten eggs and often milk or cream, then pan-fried", "categories": ["Breakfast"], "price": "10", "ingredients": []}, {"dish": "YELLOWFIN TUNA CARPACIO", "description": "Blood orange vin, mandarin, cucumber, green onions, organic basil oil.", "categories": ["Raw Bar / Crudo"], "price": "52", "ingredients": []}, {"dish": "LINGUINE AI FRUTTI DI MARE", "description": "Linguine, shrimp, clams, salmon, lobster, calabrian citrus, pomodoro. EVOO", "categories": ["Pasta Fresca"], "price": "33", "ingredients": []}, {"dish": "OYSTERS OF THE DAY  \u00bd dz", "description": "West or East coast, grapefruit and tomato mignonette, basil pesto drops, fresh greens", "categories": ["Raw Bar / Crudo"], "price": "22", "ingredients": []}, {"dish": "CAPRESE SALAD", "description": "Heirloom tomato, grill roasted scallions, sweet/sour aged balsamic, fresh burrata", "categories": [], "price": "25", "ingredients": []}, {"dish": "PIZZA SAN DANIELE", "description": "Mozzarella, cherry tomatoes, San Daniele Prosciutto, arugula", "categories": ["Pizza"], "price": "31", "ingredients": []}, {"dish": "SHORT RIB TORTELLINI", "description": "Braised short rib filing, pecorino brown butter, parmigiana sauce, pancetta, crispy prosciutto", "categories": ["Pasta Fresca"], "price": "32", "ingredients": []}, {"dish": "Fruit Salads", "description": "Consist of various kinds of fruit", "categories": ["Breakfast"], "price": "17", "ingredients": []}, {"dish": "Avocado toast", "description": "light meal", "categories": ["Breakfast"], "price": "12", "ingredients": []}, {"dish": "PIZZA PISTACCHIOSA", "description": "Mozzarella, mortadella al pistacchio, stracciatella, crushed pistachio, basil pesto", "categories": ["Pizza"], "price": "31", "ingredients": []}, {"dish": "Avocado Toast", "description": "open sandwich, consisting of toast and mashed avocado", "categories": ["Breakfast"], "price": "21", "ingredients": []}, {"dish": "IL RISTORANTE'S LASAGNA", "description": "Bechamel, bolognese, organic basil cream, mozzarella", "categories": ["Pasta Fresca"], "price": "32", "ingredients": []}, {"dish": "RISOTTO", "description": "Arborio rice, sweet corn puree, roasted heir covert, caramel cippolinis, veal reduction, EVOO", "categories": ["Pasta Fresca"], "price": "36", "ingredients": []}, {"dish": "RIGATONI", "description": "Kettle one vodka, parmesan cream, tomato sauce, basil, shaved grana", "categories": ["Pasta Fresca"], "price": "28", "ingredients": []}, {"dish": "FETTUCCINE AL PESTO", "description": "Basil, almonds, 18-month aged parmesan, fresh burrata, EVOO", "categories": ["Pasta Fresca"], "price": "27", "ingredients": []}, {"dish": "Pastries", "description": "Small portion of Cake", "categories": ["Breakfast"], "price": "12", "ingredients": []}, {"dish": "TAGLIERE SALUMI E FORMAGGI", "description": "Prosciutto, mortadella, roast beef, strawberry mustarda, toast points, marinated olives", "categories": ["Appetizers"], "price": "36", "ingredients": []}]}, {"menu_name": "First Menu", "menu_availability_week_days": ["sun", "mon", "tue", "wed", "thu", "fri", "sat"], "dishes": [{"dish": "Greek Salad", "description": "Desc", "categories": ["Salads"], "price": "200", "ingredients": []}, {"dish": "Test First Item 1234", "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. ", "categories": ["Main Course"], "price": "120.00", "ingredients": []}, {"dish": "Onion Bread", "description": "desc", "categories": ["Salads"], "price": "100.00", "ingredients": []}, {"dish": "Savory Party Bread", "description": "It's impossible to stop nibbling on warm pieces of this cheesy, oniony bread. The sliced loaf fans out for a fun presentation. It's one of the best savory appetizers I've found", "categories": ["Appetizers"], "price": "250", "ingredients": []}, {"dish": "Arabic Fattoush", "description": "This fattoush recipe is a colorful tossed salad with a lemony garlic dressing. Fattoush is one of the most well-known Middle Eastern salads and a standard dish on the mezza (small dishes) table.", "categories": ["Salads"], "price": "343.00", "ingredients": []}, {"dish": "Cedar-Plank Salmon", "description": "desc is here", "categories": ["Main Course"], "price": "120", "ingredients": []}, {"dish": "Chilli con carne recipe", "description": "Ddd", "categories": ["Main Course"], "price": "", "ingredients": []}, {"dish": "Chicken cream", "description": "desc", "categories": ["Main Course"], "price": "", "ingredients": []}, {"dish": "Garlic Bread", "description": "dfd", "categories": ["Appetizers"], "price": "22323", "ingredients": []}, {"dish": "Teriyaki Meatballs", "description": "sa", "categories": [""], "price": "343", "ingredients": []}, {"dish": "Russian salad", "description": "ets", "categories": ["Salads"], "price": "111", "ingredients": []}, {"dish": "Anzac biscuit tarts", "description": "ABC  E", "categories": ["Deserts"], "price": "33.00", "ingredients": []}, {"dish": "Cheese Bread", "description": "desc", "categories": [""], "price": "12.25", "ingredients": []}, {"dish": "Caesar Salad Supreme", "description": "asdf", "categories": ["Deserts"], "price": "22", "ingredients": ["Carrots", "Cucumbers"]}, {"dish": "Kesar Mango Lassi", "description": "The Kesar Mango Lassi Recipe is a refreshing summer drink made from mangoes, almonds, saffron and buttermilk.", "categories": ["Main Course"], "price": "3", "ingredients": []}, {"dish": "Pasta salad", "description": "tst", "categories": ["Salads"], "price": "2525", "ingredients": []}, {"dish": "Toad-in-the-hole", "description": "item2 desc", "categories": ["Main Course"], "price": "150.00", "ingredients": []}]}, {"menu_name": "Third Menus", "dishes": [{"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": []}, {"dish": "GRILLED PROVOLONE SUB", "description": "Provolone cheese cooked on the grill, with red onions and oil and vinegar", "categories": ["Subs"], "price": "7 9", "ingredients": []}, {"dish": "MEATBALL SUB", "description": "Meatballs, parmesan cheese, provolone cheese and marinara sauce", "categories": ["Subs"], "price": "9 12", "ingredients": []}, {"dish": "Vegan Tacos", "description": "Two Soft Tacos with Grilled Fajita Veggies. Topped with Avocado and Cilantro. Served with White Rice, Frijoles de Olla and Pico de Gallo.", "categories": ["Vegan"], "price": "10.95", "ingredients": []}, {"dish": "Iced Tea", "description": "House brewed, unsweetened, served with lemon", "categories": ["Drinks"], "price": "3", "ingredients": []}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": []}, {"dish": "Nachos", "description": "Tortilla Chips Topped with Ranchera Sauce and Beans, Melted Cheese, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "9.00", "ingredients": []}, {"dish": "Pizookie", "description": "Served warm with a scoop of vanilla ice cream. Great for the table. Choose chocolate chip, peanut butter, or white chocolate macadamia", "categories": ["Desserts"], "price": "11", "ingredients": []}, {"dish": "Blank Pizza", "description": "desc", "categories": [""], "price": "234", "ingredients": []}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": []}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": []}, {"dish": "Cannoli", "description": "", "categories": ["Desserts"], "price": "3", "ingredients": []}, {"dish": "Molten Chocolate Cake", "description": "Rich dark chocolate cake topped with warm chocolate sauce", "categories": ["Desserts"], "price": "8", "ingredients": []}, {"dish": "Quesadilla Vegetariana", "description": "Large Flour Tortilla with Jack Cheese, Roasted Peppers, Zucchini, Mushrooms, Corn, Pico de Gallo. Served with Sour Cream and Guacamole.", "categories": ["Vegan"], "price": "11.95", "ingredients": []}, {"dish": "Fresh Guacamole (Half Order)", "description": "", "categories": ["Appetizers"], "price": "7.00", "ingredients": []}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": []}, {"dish": "Vegan Flautas", "description": "Two Potato Flautas topped with Cabbage, Tomatoes and Sliced Avocados. Served with Hot Salsa, White Rice and Frijoles de Olla.", "categories": ["Vegan"], "price": "10.95", "ingredients": []}, {"dish": "6 pc Wing Combo", "description": "6 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "9", "ingredients": []}, {"dish": "Ceviche Mini Tostada", "description": "Shrimp & Crab Marinated with Lemon Juice and Clamato with Cucumber, Celery & Pico de Gallo. Topped with Avocado Slices and a Lemon Wedge.", "categories": ["Appetizers"], "price": "9.50", "ingredients": []}, {"dish": "Albondigas (Vegetables Only Full Order)", "description": "", "categories": ["Soup"], "price": "7.25", "ingredients": []}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": []}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": []}, {"dish": "GREEK", "description": "Romaine, kalamata olives, cucumber, tomatoes, onions, bell pepper, chickpeas, feta cheese with Greek dressing", "categories": ["Salads"], "price": "13", "ingredients": []}, {"dish": "Soda", "description": "(cola, diet, lemon-lime, cherry)", "categories": ["Drinks"], "price": "2", "ingredients": []}, {"dish": "Quesadilla Vegetariana (Add Pork)", "description": "", "categories": ["Vegan"], "price": "14.95", "ingredients": []}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": []}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": []}, {"dish": "Vegan Fajitas", "description": "Saut\u00e9ed Onions, Bell Peppers, Tomatoes, Mushrooms, Zucchini and Potatoes. Served on a Sizzling Cast Iron Skillet. Served with White Rice, Frijoles de Olla, Avocado and Pico de Gallo. Choice of Tortillas.", "categories": ["Vegan"], "price": "15.95", "ingredients": []}, {"dish": "8 pc Wing Combo", "description": "8 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "13.5", "ingredients": []}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": []}, {"dish": "Salsa", "description": "desc", "categories": ["Main Course"], "price": "100", "ingredients": []}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": []}, {"dish": "Tinga Taquitos", "description": "Shredded chicken seasoned with a savory garlic chipotle sauce. Rolled in yellow gluten free corn tortillas and served with sour cream and guacamole on the side. (Served three per order).", "categories": ["Appetizers"], "price": "10.75", "ingredients": []}, {"dish": "Vegan Burrito", "description": "With Frijoles de Olla, White Rice, Pico de Gallo, Saut\u00e9ed Onions, Peppers and Tomatoes. Topped with Red Sauce and Avocados.", "categories": ["Vegan"], "price": "10.95", "ingredients": []}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": []}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": []}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": []}, {"dish": "Appetizer Combo", "description": "A Delicious Assortment of Potato Skins, Flautas, Quesadillas, Taquitos, Green Onions, Nachos or Wings Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "19.50", "ingredients": []}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": []}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": []}, {"dish": "EGGPLANT PARMESAN", "description": "Breaded eggplant, marinara sauce and provolone cheese", "categories": ["Subs"], "price": "8 10.5", "ingredients": []}, {"dish": "GRILLED CHICKEN SALAD", "description": "Chicken breast, field greens, tomatoes, onions, toasted pecans, goat cheese, dried cranberries with honey balsamic vinaigrette", "categories": ["Salads"], "price": "15.5", "ingredients": []}, {"dish": "Albondigas", "description": "Tasty Meatballs with Fresh Vegetables in a Rich Broth. (Full Order)", "categories": ["Soup"], "price": "10.25", "ingredients": []}, {"dish": "Cream Pizza", "description": "test", "categories": ["Salads"], "price": "100", "ingredients": []}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": []}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": []}, {"dish": "Carne Asada Fries", "description": "French Fries covered with Carne Asada, Jack and Cheddar Cheese, Pico de Gallo, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "14.95", "ingredients": []}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": []}, {"dish": "PITTSBURGH SALAD", "description": "Your choice of sirloin steak, chicken breast or gyro meat, iceberg lettuce, tomatoes, onions, cucumber, black olives, green pepper, pepperoncini, French fries with ranch dressing", "categories": ["Salads"], "price": "19", "ingredients": []}, {"dish": "Fresh Guacamole", "description": "Avocados seasoned with Onions, Cilantro & Spices.", "categories": ["Appetizers"], "price": "14.00", "ingredients": []}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": []}, {"dish": "Quesadilla Vegetariana (Add Chicken)", "description": "", "categories": ["Vegan"], "price": "13.95", "ingredients": []}, {"dish": "Southwestern Buffalo Wings", "description": "A Dozen Crisp & Spicy Drummettes. Garnished with Celery & Carrots Sticks. Served with Ranch Dressing.", "categories": ["Appetizers"], "price": "12.25", "ingredients": []}, {"dish": "New York Cheesecake", "description": "Topped with fresh strawberries", "categories": ["Desserts"], "price": "8.5", "ingredients": []}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": []}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": []}, {"dish": "Chicken Soup", "description": "Chicken Cooked with Vegetables and a Side of Rice.", "categories": ["Soup"], "price": "11.95", "ingredients": []}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": []}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": []}, {"dish": "Albondigas (Half Order)", "description": "", "categories": ["Soup"], "price": "5.75", "ingredients": []}, {"dish": "HOUSE SALAD", "description": "Field greens, tomatoes, onions, cucumbers, carrots, bleu cheese crumbles with white balsamic vinaigrette", "categories": ["Salads"], "price": "9.5", "ingredients": []}, {"dish": "Fresh Squeezed Lemonade", "description": "Raspberry, mango, or classic lemon", "categories": ["Drinks"], "price": "4", "ingredients": []}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": []}, {"dish": "Costa Azul Ala Carte", "description": "6 Bacon wrapped, Cheese Stuffed Shrimp.", "categories": ["Appetizers"], "price": "14.95", "ingredients": []}, {"dish": "BLT SUB", "description": "Served with hardwood-smoked bacon strips, lettuce, tomatoes and ranch dressing", "categories": ["Subs"], "price": "9 12", "ingredients": []}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": []}, {"dish": "CAESAR", "description": "Romaine, croutons, shaved parmesan cheese with Caesar dressing", "categories": ["Salads"], "price": "9.5", "ingredients": []}, {"dish": "Hot Fudge Brownie Sundae", "description": "Double chocolate brownies topped with vanilla ice cream and fudge sauce", "categories": ["Desserts"], "price": "6.5", "ingredients": []}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": []}, {"dish": "Tortilla Soup", "description": "With Chicken, Garden Fresh Vegetables. Topped with Cheese, Tortilla Strips, Avocado & Cilantro.", "categories": ["Soup"], "price": "11.95", "ingredients": []}, {"dish": "ITALIAN SUB", "description": "Ham, salami, capicola, Genoa salami and provolone cheese", "categories": ["Subs"], "price": "8 10", "ingredients": []}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": []}, {"dish": "10 pc Wing Combo", "description": "10 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 2002 drink", "categories": ["Wing combos"], "price": "15", "ingredients": []}, {"dish": "Root Beer House-Made Sarsaparilla", "description": "", "categories": ["Drinks"], "price": "4", "ingredients": []}, {"dish": "Three Taquitos", "description": "Three Deep Fried Corn Tortillas rolled with Your Choice of Chicken or Shredded Beef. Served with Fresh Tomatoes, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "8.75", "ingredients": []}, {"dish": "CUSTOM PIZZA", "description": "", "categories": ["Custom pizza"], "price": "18 for 18\"", "ingredients": []}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": []}, {"dish": "CHEESE STEAK", "description": "Steak, provolone cheese, fried onions and peppers", "categories": ["Subs"], "price": "9 12", "ingredients": []}, {"dish": "Coc\u00eddo", "description": "A Traditional Soup with Beef & Vegetables and a Side of Rice.", "categories": ["Soup"], "price": "11.95", "ingredients": []}, {"dish": "Cocktails de Camaron", "description": "Shrimp Cocktail serve Traditional Mexican style or Gringo Style", "categories": ["Appetizers"], "price": "14.95", "ingredients": []}, {"dish": "Nachos (with Chicken, Beef or Pork)", "description": "", "categories": ["Appetizers"], "price": "12.00", "ingredients": []}, {"dish": "GRILLED SEAFOOD SALAD", "description": "Your choice of salmon or shrimp with spinach, onions, candied pecans, dried cranberries, feta cheese, bacon, with strawberry vinaigrette", "categories": ["Salads"], "price": "18", "ingredients": []}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": []}, {"dish": "Flautas", "description": "Two Deep Fried Flour Tortillas with your choice of Chicken or Shredded Beef. Served with Fresh Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "12.00", "ingredients": []}, {"dish": "Italian Soda", "description": "Raspberry, mango, strawberry, cherry, pineapple, lime, lemon, or passionfruit", "categories": ["Drinks"], "price": "4.5", "ingredients": []}]}, {"menu_name": "New1", "dishes": [{"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["milk", "ice cream", "flavoring"]}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["various fruits"]}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": ["chicken", "bread crumbs", "seasoning"]}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": ["chicken", "spicy sauce", "vegetables"]}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": ["orange juice"]}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": ["potatoes", "oil", "salt"]}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": ["beef patty", "cheese", "bun", "lettuce", "tomato", "onion", "condiments"]}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": ["tea", "ice", "water", "lemon"]}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": ["coffee beans", "water", "milk", "sugar"]}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["tea", "lemon", "water", "ice", "sugar"]}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["bread", "filling"]}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": ["bread", "cheese", "mayonnaise", "lettuce", "tomato"]}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": ["chicken patty", "bun", "lettuce", "tomato", "onion", "condiments"]}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": ["liquor", "mixer", "ice", "garnish"]}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": ["hot dog bun", "sausage", "mustard", "ketchup", "relish"]}]}, {"menu_name": "A test", "dishes": [{"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": ["Hot dog sausage", "Bun", "Ketchup", "Mustard", "Relish"]}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["Bread", "Filling"]}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": ["Beef patty", "Cheese", "Bun", "Lettuce", "Tomato", "Onion", "Pickles", "Mayonnaise", "Ketchup", "Mustard"]}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["Milk", "Ice cream", "Syrup"]}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": ["Chicken patty", "Bun", "Lettuce", "Tomato", "Onion", "Pickles", "Mayonnaise", "Ketchup", "Mustard"]}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["Fruits"]}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": ["Liquor", "Mixers", "Ice"]}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": ["Bread", "Cheese", "Butter", "Mayonnaise"]}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": ["Chicken", "Breadcrumbs"]}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": ["Coffee beans", "Water", "Milk", "Sugar"]}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": ["Chicken", "Spices", "Chillies"]}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["Tea", "Lemon", "Sugar"]}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": ["Orange Juice"]}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": ["Potatoes", "Oil"]}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": ["Tea", "Ice", "Lemon"]}]}, {"menu_name": "ABC", "dishes": [{"dish": "Quesadilla Vegetariana", "description": "Large Flour Tortilla with Jack Cheese, Roasted Peppers, Zucchini, Mushrooms, Corn, Pico de Gallo. Served with Sour Cream and Guacamole.", "categories": ["Vegan"], "price": "$11.95", "ingredients": []}, {"dish": "Cocktails de Camaron", "description": "Shrimp Cocktail serve Traditional Mexican style or Gringo Style", "categories": ["Appetizers"], "price": "$14.95", "ingredients": []}, {"dish": "Carne Asada Fries", "description": "French Fries covered with Carne Asada, Jack and Cheddar Cheese, Pico de Gallo, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$14.95", "ingredients": []}, {"dish": "Tinga Taquitos", "description": "Shredded chicken seasoned with a savory garlic chipotle sauce. Rolled in yellow gluten free corn tortillas and served with sour cream and guacamole on the side. (Served three per order).", "categories": ["Appetizers"], "price": "$10.75", "ingredients": []}, {"dish": "Ceviche Mini Tostada", "description": "Shrimp & Crab Marinated with Lemon Juice and Clamato with Cucumber, Celery & Pico de Gallo. Topped with Avocado Slices and a Lemon Wedge.", "categories": ["Appetizers"], "price": "$9.50", "ingredients": []}, {"dish": "Vegan Tacos", "description": "Two Soft Tacos with Grilled Fajita Veggies. Topped with Avocado and Cilantro. Served with White Rice, Frijoles de Olla and Pico de Gallo.", "categories": ["Vegan"], "price": "$10.95", "ingredients": []}, {"dish": "Vegan Flautas", "description": "Two Potato Flautas topped with Cabbage, Tomatoes and Sliced Avocados. Served with Hot Salsa, White Rice and Frijoles de Olla.", "categories": ["Vegan"], "price": "$10.95", "ingredients": []}, {"dish": "Appetizer Combo", "description": "A Delicious Assortment of Potato Skins, Flautas, Quesadillas, Taquitos, Green Onions, Nachos or Wings Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$19.50", "ingredients": []}, {"dish": "Chicken Soup", "description": "Chicken Cooked with Vegetables and a Side of Rice.", "categories": ["Soup"], "price": "$11.95", "ingredients": []}, {"dish": "Albondigas (Vegetables Only)", "description": "Tasty Meatballs with Fresh Vegetables in a Rich Broth.", "categories": ["Soup"], "price": "$7.25", "ingredients": []}, {"dish": "Nachos (with Chicken, Beef or Pork)", "description": "Tortilla Chips Topped with Ranchera Sauce and Beans, Melted Cheese, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$12.00", "ingredients": []}, {"dish": "Tortilla Soup", "description": "With Chicken, Garden Fresh Vegetables. Topped with Cheese, Tortilla Strips, Avocado & Cilantro.", "categories": ["Soup"], "price": "$11.95", "ingredients": []}, {"dish": "Quesadilla Vegetariana (Add Chicken)", "description": "Large Flour Tortilla with Jack Cheese, Roasted Peppers, Zucchini, Mushrooms, Corn, Pico de Gallo. Served with Sour Cream and Guacamole.", "categories": ["Vegan"], "price": "$13.95", "ingredients": []}, {"dish": "Fresh Guacamole (Half Order)", "description": "Avocados seasoned with Onions, Cilantro & Spices.", "categories": ["Appetizers"], "price": "$7.00", "ingredients": []}, {"dish": "Albondigas (Half Order)", "description": "Tasty Meatballs with Fresh Vegetables in a Rich Broth.", "categories": ["Soup"], "price": "$5.75", "ingredients": []}, {"dish": "Costa Azul Ala Carte", "description": "6 Bacon wrapped, Cheese Stuffed Shrimp.", "categories": ["Appetizers"], "price": "$14.95", "ingredients": []}, {"dish": "Quesadilla Vegetariana (Add Pork)", "description": "Large Flour Tortilla with Jack Cheese, Roasted Peppers, Zucchini, Mushrooms, Corn, Pico de Gallo. Served with Sour Cream and Guacamole.", "categories": ["Vegan"], "price": "$14.95", "ingredients": []}, {"dish": "Coc\u00eddo", "description": "A Traditional Soup with Beef & Vegetables and a Side of Rice.", "categories": ["Soup"], "price": "$11.95", "ingredients": []}, {"dish": "Nachos", "description": "Tortilla Chips Topped with Ranchera Sauce and Beans, Melted Cheese, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$9.00", "ingredients": []}, {"dish": "Southwestern Buffalo Wings", "description": "A Dozen Crisp & Spicy Drummettes. Garnished with Celery & Carrots Sticks. Served with Ranch Dressing.", "categories": ["Appetizers"], "price": "$12.25", "ingredients": []}, {"dish": "Three Taquitos", "description": "Three Deep Fried Corn Tortillas rolled with Your Choice of Chicken or Shredded Beef. Served with Fresh Tomatoes, Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$8.75", "ingredients": []}, {"dish": "Fresh Guacamole", "description": "Avocados seasoned with Onions, Cilantro & Spices.", "categories": ["Appetizers"], "price": "$14.00", "ingredients": []}, {"dish": "Vegan Burrito", "description": "With Frijoles de Olla, White Rice, Pico de Gallo, Saut\u00e9ed Onions, Peppers and Tomatoes. Topped with Red Sauce and Avocados.", "categories": ["Vegan"], "price": "$10.95", "ingredients": []}, {"dish": "Flautas", "description": "Two Deep Fried Flour Tortillas with your choice of Chicken or Shredded Beef. Served with Fresh Guacamole & Sour Cream.", "categories": ["Appetizers"], "price": "$12.00", "ingredients": []}, {"dish": "Vegan Fajitas", "description": "Saut\u00e9ed Onions, Bell Peppers, Tomatoes, Mushrooms, Zucchini and Potatoes. Served on a Sizzling Cast Iron Skillet. Served with White Rice, Frijoles de Olla, Avocado and Pico de Gallo. Choice of Tortillas.", "categories": ["Vegan"], "price": "$15.95", "ingredients": []}, {"dish": "Albondigas", "description": "Tasty Meatballs with Fresh Vegetables in a Rich Broth. (Full Order)", "categories": ["Soup"], "price": "$10.25", "ingredients": []}]}, {"menu_name": "New menu", "dishes": [{"dish": "Cannoli", "description": "", "categories": ["Desserts"], "price": "3", "ingredients": []}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": ["cheese", "bread", "butter", "mayonnaise"]}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["various fruits"]}, {"dish": "Hot Fudge Brownie Sundae", "description": "Double chocolate brownies topped with vanilla ice cream and fudge sauce", "categories": ["Desserts"], "price": "6.5", "ingredients": ["brownies", "vanilla ice cream", "fudge sauce"]}, {"dish": "MEATBALL SUB", "description": "Meatballs, parmesan cheese, provolone cheese and marinara sauce", "categories": ["Subs"], "price": "9 12", "ingredients": ["meatballs", "parmesan cheese", "provolone cheese", "marinara sauce"]}, {"dish": "CAESAR", "description": "Romaine, croutons, shaved parmesan cheese with Caesar dressing", "categories": ["Salads"], "price": "9.5", "ingredients": ["Romaine lettuce", "croutons", "shaved parmesan cheese", "Caesar dressing"]}, {"dish": "BLT SUB", "description": "Served with hardwood-smoked bacon strips, lettuce, tomatoes and ranch dressing", "categories": ["Subs"], "price": "9 12", "ingredients": ["hardwood-smoked bacon strips", "lettuce", "tomatoes", "ranch dressing"]}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["bread", "filling"]}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["milk", "ice cream", "flavorings"]}, {"dish": "Molten Chocolate Cake", "description": "Rich dark chocolate cake topped with warm chocolate sauce", "categories": ["Desserts"], "price": "8", "ingredients": ["chocolate cake", "chocolate sauce"]}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": ["chicken", "breading"]}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": ["tea", "ice", "sugar"]}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": ["hot dog sausage", "bun", "mustard", "ketchup", "relish"]}, {"dish": "Pizookie", "description": "Served warm with a scoop of vanilla ice cream. Great for the table. Choose chocolate chip, peanut butter, or white chocolate macadamia", "categories": ["Desserts"], "price": "11", "ingredients": ["cookie", "vanilla ice cream", "chocolate chip", "peanut butter", "white chocolate", "macadamia nuts"]}, {"dish": "GREEK", "description": "Romaine, kalamata olives, cucumber, tomatoes, onions, bell pepper, chickpeas, feta cheese with Greek dressing", "categories": ["Salads"], "price": "13", "ingredients": ["Romaine lettuce", "kalamata olives", "cucumber", "tomatoes", "onions", "bell pepper", "chickpeas", "feta cheese", "Greek dressing"]}, {"dish": "HOUSE SALAD", "description": "Field greens, tomatoes, onions, cucumbers, carrots, bleu cheese crumbles with white balsamic vinaigrette", "categories": ["Salads"], "price": "9.5", "ingredients": ["field greens", "tomatoes", "onions", "cucumbers", "carrots", "bleu cheese crumbles", "white balsamic vinaigrette"]}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": ["chicken", "spicy sauce", "vegetables"]}, {"dish": "8 pc Wing Combo", "description": "8 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "13.5", "ingredients": ["wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "Root Beer", "description": "House-Made Sarsaparilla", "categories": ["Drinks"], "price": "4", "ingredients": ["root beer", "sarsaparilla"]}, {"dish": "EGGPLANT PARMESAN", "description": "Breaded eggplant, marinara sauce and provolone cheese", "categories": ["Subs"], "price": "8 10.5", "ingredients": ["breaded eggplant", "marinara sauce", "provolone cheese"]}, {"dish": "GRILLED CHICKEN SALAD", "description": "Chicken breast, field greens, tomatoes, onions, toasted pecans, goat cheese, dried cranberries with honey balsamic vinaigrette", "categories": ["Salads"], "price": "15.5", "ingredients": ["chicken breast", "field greens", "tomatoes", "onions", "toasted pecans", "goat cheese", "dried cranberries", "honey balsamic vinaigrette"]}, {"dish": "ITALIAN SUB", "description": "Ham, salami, capicola, Genoa salami and provolone cheese", "categories": ["Subs"], "price": "8 10", "ingredients": ["ham", "salami", "capicola", "Genoa salami", "provolone cheese"]}, {"dish": "10 pc Wing Combo", "description": "10 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "15", "ingredients": ["wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": ["beef patty", "cheese", "bun", "lettuce", "tomato", "onion", "pickles", "condiments"]}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": ["alcohol", "mixers", "garnish"]}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["tea", "lemon", "ice", "sugar"]}, {"dish": "GRILLED PROVOLONE SUB", "description": "Provolone cheese cooked on the grill, with red onions and oil and vinegar", "categories": ["Subs"], "price": "7 9", "ingredients": ["provolone cheese", "red onions", "oil", "vinegar"]}, {"dish": "New York Cheesecake", "description": "Topped with fresh strawberries", "categories": ["Desserts"], "price": "8.5", "ingredients": ["cheesecake", "strawberries"]}, {"dish": "Soda", "description": "(cola, diet, lemon-lime, cherry)", "categories": ["Drinks"], "price": "2", "ingredients": ["cola", "diet soda", "lemon-lime", "cherry"]}, {"dish": "CUSTOM PIZZA", "description": "Starting at 18 for 18\"", "categories": ["Custom pizza"], "price": "18", "ingredients": []}, {"dish": "6 pc Wing Combo", "description": "6 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "9", "ingredients": ["wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "GRILLED SEAFOOD SALAD", "description": "Your choice of salmon or shrimp with spinach, onions, candied pecans, dried cranberries, feta cheese, bacon, with strawberry vinaigrette", "categories": ["Salads"], "price": "18", "ingredients": ["salmon", "shrimp", "spinach", "onions", "candied pecans", "dried cranberries", "feta cheese", "bacon", "strawberry vinaigrette"]}, {"dish": "PITTSBURGH SALAD", "description": "Your choice of sirloin steak, chicken breast or gyro meat, iceberg lettuce, tomatoes, onions, cucumber, black olives, green pepper, pepperoncini, French fries with ranch dressing", "categories": ["Salads"], "price": "19", "ingredients": ["sirloin steak", "chicken breast", "gyro meat", "iceberg lettuce", "tomatoes", "onions", "cucumber", "black olives", "green pepper", "pepperoncini", "French fries", "ranch dressing"]}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": ["coffee beans", "water", "milk", "sugar"]}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": ["orange juice"]}, {"dish": "Italian Soda", "description": "Raspberry, mango, strawberry, cherry, pineapple, lime, lemon, or passionfruit", "categories": ["Drinks"], "price": "4.5", "ingredients": ["Italian soda", "raspberry", "mango", "strawberry", "cherry", "pineapple", "lime", "lemon", "passionfruit"]}, {"dish": "Fresh Squeezed Lemonade", "description": "Raspberry, mango, or classic lemon", "categories": ["Drinks"], "price": "4", "ingredients": ["lemonade", "raspberry", "mango", "lemon"]}, {"dish": "Iced Tea", "description": "House brewed, unsweetened, served with lemon", "categories": ["Drinks"], "price": "3", "ingredients": ["iced tea", "lemon"]}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": ["chicken patty", "bun", "lettuce", "tomato", "onion", "pickles", "condiments"]}, {"dish": "CHEESE STEAK", "description": "Steak, provolone cheese, fried onions and peppers", "categories": ["Subs"], "price": "9 12", "ingredients": ["steak", "provolone cheese", "fried onions", "peppers"]}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": ["potatoes", "oil", "salt", "spices"]}]}, {"menu_name": "New test", "dishes": [{"dish": "Pizookie", "description": "Served warm with a scoop of vanilla ice cream. Great for the table. Choose chocolate chip, peanut butter, or white chocolate macadamia", "categories": ["Desserts"], "price": "11", "ingredients": ["Cookie dough", "Vanilla ice cream", "Chocolate chips", "Peanut butter", "White chocolate", "Macadamia"]}, {"dish": "GRILLED CHICKEN SALAD", "description": "Chicken breast, field greens, tomatoes, onions, toasted pecans, goat cheese, dried cranberries with honey balsamic vinaigrette", "categories": ["Salads"], "price": "15.5", "ingredients": ["Chicken breast", "field greens", "tomatoes", "onions", "toasted pecans", "goat cheese", "dried cranberries", "honey balsamic vinaigrette"]}, {"dish": "GRILLED PROVOLONE SUB", "description": "Provolone cheese cooked on the grill, with red onions and oil and vinegar", "categories": ["Subs"], "price": "7", "ingredients": ["Provolone cheese", "red onions", "oil", "vinegar"]}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["milk", "ice cream", "flavoring"]}, {"dish": "GRILLED SEAFOOD SALAD", "description": "Your choice of salmon or shrimp with spinach, onions, candied pecans, dried cranberries, feta cheese, bacon, with strawberry vinaigrette", "categories": ["Salads"], "price": "18", "ingredients": ["Salmon", "shrimp", "spinach", "onions", "candied pecans", "dried cranberries", "feta cheese", "bacon", "strawberry vinaigrette"]}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "3", "ingredients": ["tea", "lemon"]}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "12", "ingredients": ["alcohol", "mixers", "garnish"]}, {"dish": "Italian Soda", "description": "Raspberry, mango, strawberry, cherry, pineapple, lime, lemon, or passionfruit", "categories": ["Drinks"], "price": "4.5", "ingredients": ["Raspberry", "Mango", "Strawberry", "Cherry", "Pineapple", "Lime", "Lemon", "Passionfruit"]}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "23", "ingredients": ["chicken patty", "bun", "lettuce", "tomato", "onion", "mayonnaise", "ketchup"]}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["various fruits"]}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "15", "ingredients": ["potatoes", "oil", "salt"]}, {"dish": "New York Cheesecake", "description": "Topped with fresh strawberries", "categories": ["Desserts"], "price": "8.5", "ingredients": ["Cheesecake", "Strawberries"]}, {"dish": "Cannoli", "description": "", "categories": ["Desserts"], "price": "3", "ingredients": []}, {"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "5", "ingredients": ["coffee beans", "water"]}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "22", "ingredients": ["bread", "cheese", "butter"]}, {"dish": "EGGPLANT PARMESAN", "description": "Breaded eggplant, marinara sauce and provolone cheese", "categories": ["Subs"], "price": "8", "ingredients": ["Breaded eggplant", "marinara sauce", "provolone cheese"]}, {"dish": "CHEESE STEAK", "description": "Steak, provolone cheese, fried onions and peppers", "categories": ["Subs"], "price": "9", "ingredients": ["Steak", "provolone cheese", "fried onions", "peppers"]}, {"dish": "GREEK", "description": "Romaine, kalamata olives, cucumber, tomatoes, onions, bell pepper, chickpeas, feta cheese with Greek dressing", "categories": ["Salads"], "price": "13", "ingredients": ["Romaine", "kalamata olives", "cucumber", "tomatoes", "onions", "bell pepper", "chickpeas", "feta cheese", "Greek dressing"]}, {"dish": "Soda", "description": "(cola, diet, lemon-lime, cherry)", "categories": ["Drinks"], "price": "2", "ingredients": ["Cola", "Diet soda", "Lemon-lime", "Cherry"]}, {"dish": "MEATBALL SUB", "description": "Meatballs, parmesan cheese, provolone cheese and marinara sauce", "categories": ["Subs"], "price": "9", "ingredients": ["Meatballs", "parmesan cheese", "provolone cheese", "marinara sauce"]}, {"dish": "PITTSBURGH SALAD", "description": "Your choice of sirloin steak, chicken breast or gyro meat, iceberg lettuce, tomatoes, onions, cucumber, black olives, green pepper, pepperoncini, French fries with ranch dressing", "categories": ["Salads"], "price": "19", "ingredients": ["Sirloin steak", "chicken breast", "gyro meat", "iceberg lettuce", "tomatoes", "onions", "cucumber", "black olives", "green pepper", "pepperoncini", "French fries", "ranch dressing"]}, {"dish": "10 pc Wing Combo", "description": "10 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "15", "ingredients": ["Wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "34", "ingredients": ["beef patty", "cheese", "bun", "lettuce", "tomato", "onion", "mayonnaise", "ketchup"]}, {"dish": "HOUSE SALAD", "description": "Field greens, tomatoes, onions, cucumbers, carrots, bleu cheese crumbles with white balsamic vinaigrette", "categories": ["Salads"], "price": "9.5", "ingredients": ["Field greens", "tomatoes", "onions", "cucumbers", "carrots", "bleu cheese crumbles", "white balsamic vinaigrette"]}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "2", "ingredients": ["tea", "ice"]}, {"dish": "Fresh Squeezed Lemonade", "description": "Raspberry, mango, or classic lemon", "categories": ["Drinks"], "price": "4", "ingredients": ["Raspberry", "Mango", "Lemon"]}, {"dish": "6 pc Wing Combo", "description": "6 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "9", "ingredients": ["Wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "33", "ingredients": ["chicken", "spices"]}, {"dish": "BLT SUB", "description": "Served with hardwood-smoked bacon strips, lettuce, tomatoes and ranch dressing", "categories": ["Subs"], "price": "9", "ingredients": ["Hardwood-smoked bacon strips", "lettuce", "tomatoes", "ranch dressing"]}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "14", "ingredients": ["chicken", "bread crumbs"]}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "13", "ingredients": ["bread", "filling"]}, {"dish": "Iced Tea", "description": "House brewed, unsweetened, served with lemon", "categories": ["Drinks"], "price": "3", "ingredients": ["Iced tea", "Lemon"]}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "24", "ingredients": ["hot dog bun", "sausage", "mustard", "ketchup"]}, {"dish": "Root Beer", "description": "House-Made Sarsaparilla", "categories": ["Drinks"], "price": "4", "ingredients": ["Root beer", "Sarsaparilla"]}, {"dish": "Molten Chocolate Cake", "description": "Rich dark chocolate cake topped with warm chocolate sauce", "categories": ["Desserts"], "price": "8", "ingredients": ["Chocolate cake", "Chocolate sauce"]}, {"dish": "8 pc Wing Combo", "description": "8 Boneless or Classic (Bone-In) wings with up to 2 flavors, regular fries or veggie sticks, 1 dip and a 20oz drink", "categories": ["Wing combos"], "price": "13.5", "ingredients": ["Wings", "fries", "veggie sticks", "dip", "drink"]}, {"dish": "Hot Fudge Brownie Sundae", "description": "Double chocolate brownies topped with vanilla ice cream and fudge sauce", "categories": ["Desserts"], "price": "6.5", "ingredients": ["Brownies", "Vanilla ice cream", "Fudge sauce"]}, {"dish": "CAESAR", "description": "Romaine, croutons, shaved parmesan cheese with Caesar dressing", "categories": ["Salads"], "price": "9.5", "ingredients": ["Romaine", "croutons", "shaved parmesan cheese", "Caesar dressing"]}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "4", "ingredients": ["oranges"]}, {"dish": "ITALIAN SUB", "description": "Ham, salami, capicola, Genoa salami and provolone cheese", "categories": ["Subs"], "price": "8", "ingredients": ["Ham", "salami", "capicola", "Genoa salami", "provolone cheese"]}, {"dish": "CUSTOM PIZZA", "description": "Starting at 18 for 18\"", "categories": ["Custom pizza"], "price": "18", "ingredients": []}]}, {"menu_name": "Forth menu", "dishes": [{"dish": "Coffee", "description": "", "categories": ["Beverages"], "price": "$5", "ingredients": []}, {"dish": "Spicy chicken", "description": "", "categories": ["Main course"], "price": "$33", "ingredients": []}, {"dish": "Sandwich", "description": "", "categories": ["Appetizers"], "price": "$13", "ingredients": []}, {"dish": "Cheese sandwich", "description": "", "categories": ["Main course"], "price": "$22", "ingredients": []}, {"dish": "Orange-Glazed Meatballs", "description": "Desc 1", "categories": ["Appetizers"], "price": "100", "ingredients": []}, {"dish": "Nuggets", "description": "", "categories": ["Appetizers"], "price": "$14", "ingredients": []}, {"dish": "Hot Spinach Artichoke Dip", "description": "Land 2", "categories": ["Appetizers"], "price": "200", "ingredients": []}, {"dish": "Test1", "description": "des", "categories": ["Asdfasdf"], "price": "242", "ingredients": []}, {"dish": "Lemon Tea", "description": "", "categories": ["Beverages"], "price": "$3", "ingredients": []}, {"dish": "Iced Tea", "description": "", "categories": ["Beverages"], "price": "$2", "ingredients": []}, {"dish": "French Fries", "description": "", "categories": ["Appetizers"], "price": "$15", "ingredients": []}, {"dish": "Cocktails", "description": "", "categories": ["Appetizers"], "price": "$12", "ingredients": []}, {"dish": "Orange Juice", "description": "", "categories": ["Beverages"], "price": "$4", "ingredients": []}, {"dish": "Cheeseburger", "description": "", "categories": ["Main course"], "price": "$34", "ingredients": []}, {"dish": "Grilled Tomato-Peach Pizza", "description": "Rev desc", "categories": ["Appetizers"], "price": "150", "ingredients": []}, {"dish": "Fruit Salad", "description": "", "categories": ["Appetizers"], "price": "$13", "ingredients": []}, {"dish": "Roast Chicken with Grapefruit", "description": "Bacon, grapefruit, and a flamb\u00e9ed sauce turn a basic roast chicken into a holiday homerun in under two hours", "categories": ["Main Course"], "price": "100.00", "ingredients": []}, {"dish": "Spicy Chicken Jerky", "description": "Chicken jerky", "categories": ["Main Course"], "price": "200.00", "ingredients": []}, {"dish": "Cheese Biscuit Stacks", "description": "my desc", "categories": ["Appetizers"], "price": "33", "ingredients": []}, {"dish": "Milk Shake", "description": "", "categories": ["Beverages"], "price": "$3", "ingredients": []}, {"dish": "Hot dog", "description": "", "categories": ["Main course"], "price": "$24", "ingredients": []}, {"dish": "Chicken burgers", "description": "", "categories": ["Main course"], "price": "$23", "ingredients": []}, {"dish": "Fruit and Cheese Board", "description": "Scape 1", "categories": ["Appetizers"], "price": "140", "ingredients": []}]}]

The response should be in form of chat response. From the details given above answer the questions`;
var pURL = "http://localhost:8083/";
    </script>

    <title>React App</title>
  </head>
  <body style="overflow:hidden;">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div class="p-1" style="background-color: #f0edf0; border-radius: 10px;">
		<div id="root" style="background-color: #ffffff; border-radius: 10px;box-shadow: rgba(100, 100, 111, 0.1) 0px 7px 29px 0px;"></div>
    </div>
	
	<!-- <div id="root" style="background-color: #ffffff; border-radius: 10px;"></div> -->
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
