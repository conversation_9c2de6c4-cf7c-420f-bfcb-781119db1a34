@echo off
setlocal enabledelayedexpansion

:: Set Variables
set VARIABLE=%CD%
for %%I in ("%VARIABLE%") do set VARIABLE=%%~nxI
set BUILD_PATH=build
set DEST_BASE_PATH=C:\custom\projects\vipdigs\python\static
set SECTIONS_ROOT=C:\custom\projects\vipdigs\python\sections

:: Run React Build
echo Running React Build...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo React build failed!
    pause
    exit /b
)
echo Build completed successfully.
timeout /t 5 /nobreak >nul
:: Define destination folders
set JS_DEST=%DEST_BASE_PATH%\js\%VARIABLE%
set CSS_DEST=%DEST_BASE_PATH%\css\%VARIABLE%

:: Delete old files
echo Deleting old JS, CSS, and MAP files...
if exist "%JS_DEST%" (
    del /Q "%JS_DEST%\*" 2>nul
) else (
    mkdir "%JS_DEST%"
)
if exist "%CSS_DEST%" (
    del /Q "%CSS_DEST%\*" 2>nul
) else (
    mkdir "%CSS_DEST%"
)

:: Copy new files
echo Copying new files...
xcopy /Y /Q "%BUILD_PATH%\static\js\*" "%JS_DEST%\"
xcopy /Y /Q "%BUILD_PATH%\static\css\*" "%CSS_DEST%\"

:: Find the correct HTML file
echo Searching for the correct HTML file...
set HTML_FILE=
for /r "%SECTIONS_ROOT%" %%F in (*.html) do (
    findstr /C:"js/%VARIABLE%/" "%%F" >nul
    if not errorlevel 1 (
        set HTML_FILE=%%F
        goto FOUND_HTML
    )
)

echo Error: No matching HTML file found!
pause
exit /b

:FOUND_HTML
echo Found HTML file: %HTML_FILE%

:: Extract the new JS and CSS file names
set NEW_JS=
set NEW_CSS=
for %%J in ("%JS_DEST%\*.js") do set NEW_JS=%%~nxJ
for %%C in ("%CSS_DEST%\*.css") do set NEW_CSS=%%~nxC

:: Ensure variables are set
if "%NEW_JS%"=="" (
    echo Error: No new JS file found!
    pause
    exit /b
)
if "%NEW_CSS%"=="" (
    echo Error: No new CSS file found!
    pause
    exit /b
)

:: Debugging: Print file paths
echo New JS File: %NEW_JS%
echo New CSS File: %NEW_CSS%

:: Update the HTML file using PowerShell (✅ FIXED SYNTAX)
echo Updating HTML file...
powershell -Command "(Get-Content %HTML_FILE%) -replace 'js/%VARIABLE%/main.*.js', 'js/%VARIABLE%/%NEW_JS%' -replace 'css/%VARIABLE%/main.*.css', 'css/%VARIABLE%/%NEW_CSS%'| Set-Content  %HTML_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to update HTML file!
    pause
    exit /b
)

echo Update completed successfully.
pause
