import { initializeApp } from "firebase/app";
import { getAI, getGenerativeModel } from "firebase/ai";
import { getAuth } from "firebase/auth";
import {
    getFirestore, 
    collection,
    doc,
    query,
    where,
    getDocs,
    onSnapshot,
    limit,
    orderBy,
    addDoc,
    serverTimestamp,
    updateDoc
} from "firebase/firestore";
import * as pdfjsLib from "pdfjs-dist";
import pdfWorker from "pdfjs-dist/build/pdf.worker.entry";

// Set the worker source dynamically
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker;

async function extractTextFromPdfUrl(pdfUrl) {
    try {
      // Fetch the PDF file
      const response = await fetch(pdfUrl);
      if (!response.ok) throw new Error("Failed to fetch PDF");
  
      // Convert response to an ArrayBuffer (needed for PDF.js)
      const pdfBuffer = await response.arrayBuffer();
  
      // Load the PDF document
      const pdf = await pdfjsLib.getDocument(pdfBuffer).promise;
      let extractedText = "";
  
      // Loop through each page and extract text
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const text = textContent.items.map((item) => item.str).join(" ");
        extractedText += text + "\n\n"; // Separate pages with line breaks
      }
  
      return extractedText;
    } catch (error) {
      console.error("Error extracting text from PDF:", error);
      return null;
    }
  }


const firebaseConfig = window.config;

const firebaseApp = initializeApp(firebaseConfig);
const ai = getAI(firebaseApp);

const p = [{ text: window.desc }];
window.showLoader();
for(let i=0;i<window.pdfUrls.length;i++){
   const txt = await extractTextFromPdfUrl(window.pdfUrls[i]);
   p.push({text: txt}); 
}
window.hideLoader();
const systemInstruction = {
    role: "system",
    parts: p
  };

const model = getGenerativeModel(ai, { model: "gemini-2.0-flash",
    systemInstruction:systemInstruction });
//const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

const auth = getAuth(firebaseApp);
const db = getFirestore();

const setSnapShot = async(chatbotId,setChatData,setIsWaiting)=>{
    const placeDocRef = doc(db,"n_chatbot",chatbotId);
    const qry1 = query(collection(placeDocRef,"messages"),orderBy("q_dt"))
    onSnapshot(qry1,(snapshot)=>{
        const chat_data = [];
        for (const q of snapshot.docs){
            //console.log(q.data())
            const data = q.data();
            data['id'] = q.id;
            chat_data.push(data)
        }
        const last_msg = chat_data[chat_data.length-1];
        if(last_msg.a){
            setIsWaiting(false)
        }
        else{
            setIsWaiting(true)
        }
        setChatData(chat_data)
    })
}
const chatSnapShot = async (setChatData,setMainCollection,setIsWaiting)=>{
    const qry  = query(collection(db,"n_chatbot"),where("place_id","==",window.pd),where("session_id","==",window.s_d),limit(1))
    const chatSnap = await getDocs(qry);
    chatSnap.forEach((doc1) => {
        const placeDocRef = doc(db,"n_chatbot",doc1.id);
        setMainCollection(placeDocRef);
        setSnapShot(doc1.id,setChatData,setIsWaiting)
    })
}

const addQuestion = async (mainCollection,question,setChatData,setIsWaiting,setIsConnecting,setMainCollection,chatS,chatData) =>{
    try{
        let collectionRef;
        let passColl = mainCollection;
        if (mainCollection){
            setIsConnecting(false)
            collectionRef = collection(mainCollection,'messages');
        }
        else{
            setIsConnecting(true);
            const search_array = window.pn.split(" ");
            search_array.push(window.pd)
            const mPayload = {
                "place_id":window.pd,
                "session_id":window.s_d,
                "date_created":serverTimestamp(),
                "place_name":window.pn,
                "prompt_tokens":0,
                "ans_tokens":0,
                "total_tokens":0,
                "search_terms":search_array,
                "first_msg":question
            }
            const collectionRef1 = collection(db,"n_chatbot")
            const {id} = await addDoc(collectionRef1, mPayload);
            const placeDocRef = doc(db,"n_chatbot",id);
            setMainCollection(placeDocRef);
            passColl = placeDocRef;
            collectionRef = collection(placeDocRef,'messages');
            setSnapShot(id,setChatData,setIsWaiting)
        }
        const payload = {"q":question,"q_dt":serverTimestamp()}
    
        const chatInput = question;
        const docRef = await addDoc(collectionRef, payload);
        setIsConnecting(false);
        try{
            if(chatData.length<=window.mm)
            {
                const result = await chatS.sendMessage(chatInput);
                const response = await result.response;
                let resText = response.candidates[0].content.parts[0].text;
                resText = resText.replace('```html','').replace('```','').replace('\r','').replace('\n','')
                //console.log(resText)
                await updateDoc(docRef,{"a":sanitizeForFirestore(resText),"a_dt":serverTimestamp()})
                //await addDoc(collectionRef, {"a":response.candidates[0].content.parts[0].text,"a_dt":serverTimestamp()});
                //console.log('aggregated response: ', JSON.stringify(response.usageMetadata));
                const data = {
                    chatbot_id:passColl.id,
                    msg_id:docRef.id,
                    place_id:window.pd,
                    q_token:response.usageMetadata.promptTokenCount,
                    a_token:response.usageMetadata.candidatesTokenCount,
                    t_token:response.usageMetadata.totalTokenCount,
                }
                fetch(window.pURL+"api/v2/update_tokens", {
                    method: "POST",
                    mode: "cors",
                    cache: "no-cache",
                    headers: { "Content-Type": "application/json" },
                    redirect: "follow",
                    referrerPolicy: "no-referrer",
                    body: JSON.stringify(data),
                });
            }
            else{
                await updateDoc(docRef,{"a":"I am sorry, but I can’t answer more questions on this topic. Please contact the business for further questions.","a_dt":serverTimestamp()})
                setIsWaiting(false)
                setIsConnecting(false);
                
            }
        }
        catch(e){
            console.log(e)
            setIsWaiting(false);
            //updateDoc(docRef,{"a":"Sorry! This question is not in my scope","a_dt":serverTimestamp()})
        }
        
    }
    catch(e){
        console.log(e)
        setIsWaiting(false);
        setIsConnecting(false);

        //updateDoc(docRef,{"a":"Sorry! This question is not in my scope","a_dt":serverTimestamp()})
    }

}
function sanitizeForFirestore(value) {
    if (typeof value === "number") {
        return value; // Return numbers as-is
    }

    if (typeof value !== "string") {
        try {
            value = JSON.stringify(value); // Convert objects/arrays to JSON-safe strings
        } catch (e) {
            return ""; // Return empty string if serialization fails
        }
    }

    // Remove all non-printable characters (ASCII control chars + Unicode invisible chars)
    return value.replace(/[\x00-\x1F\x7F-\x9F\u200B\u200C\u200D\uFEFF]/g, "");
}
export{chatSnapShot,addQuestion,model}
